#!/usr/bin/env python3
"""
Test investment creation functionality
"""

import requests
import json

def test_investment_creation():
    """Test investment creation endpoint"""
    
    print("💰 Testing Investment Creation")
    print("=" * 40)
    
    # Test investment data
    investment_data = {
        "title": "Test Real Estate Investment",
        "amount": 100000,
        "expected_roi": 12.5,
        "risk_level": "medium",
        "investment_type": "property",
        "duration_months": 24,
        "description": "A test investment opportunity in real estate",
        "location": "Mumbai, Maharashtra"
    }
    
    print(f"📝 Investment data: {json.dumps(investment_data, indent=2)}")
    
    # Mock token for testing
    mock_token = "mock_firebase_token"
    
    try:
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/investments/",
            json=investment_data,
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {mock_token}"
            }
        )
        
        print(f"📡 Response Status: {response.status_code}")
        
        if response.status_code == 401:
            print("✅ Expected 401 error for mock token - endpoint is working")
            print("💡 To test investment creation properly:")
            print("   1. Login through the web interface")
            print("   2. Use the 'Create Investment' button")
            print("   3. Fill out the investment form")
            return True
        elif response.status_code == 201 or response.status_code == 200:
            data = response.json()
            print("✅ Investment creation successful!")
            print(f"   Investment ID: {data.get('id')}")
            print(f"   Title: {data.get('title')}")
            print(f"   Amount: ₹{data.get('amount')}")
            return True
        else:
            try:
                error_data = response.json()
                print(f"❌ Investment creation failed: {error_data.get('detail')}")
            except:
                print(f"❌ Investment creation failed: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure your FastAPI server is running")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_database_investment_creation():
    """Test investment creation directly in database"""
    
    print("\n🗄️ Testing Database Investment Creation")
    
    try:
        from app.db.session import get_db
        from app.db import crud, models
        from app.db.models import InvestmentRiskLevel
        from datetime import datetime, timedelta
        
        db = next(get_db())
        
        # Find a test user
        users = crud.get_users(db, skip=0, limit=5)
        if not users:
            print("❌ No users found in database")
            return False
        
        test_user = users[0]
        print(f"📝 Testing investment creation for user: {test_user.email} (ID: {test_user.id})")
        
        # Test investment data
        investment_data = {
            "title": "Database Test Investment",
            "amount": 50000.0,
            "expected_roi": 10.0,
            "risk_level": InvestmentRiskLevel.MEDIUM,
            "investment_type": "rental",
            "duration_months": 12,
            "description": "Test investment created directly in database",
            "location": "Delhi, India",
            "start_date": datetime.now(),
            "end_date": datetime.now() + timedelta(days=365),
            "status": "active"
        }
        
        print(f"📝 Investment data: {investment_data}")
        
        # Create investment
        db_investment = crud.create_investment(
            db=db, 
            investment_data=investment_data, 
            investor_id=test_user.id
        )
        
        if db_investment:
            print("✅ Database investment creation successful!")
            print(f"   Investment ID: {db_investment.id}")
            print(f"   Title: {db_investment.title}")
            print(f"   Amount: ₹{db_investment.amount}")
            print(f"   Investor: {test_user.email}")
            
            # Clean up - delete the test investment
            db.delete(db_investment)
            db.commit()
            print("🧹 Test investment cleaned up")
            
            return True
        else:
            print("❌ Database investment creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Database test error: {str(e)}")
        print(f"   Error type: {type(e).__name__}")
        
        # Check if it's a missing column error
        if "no such column" in str(e).lower() or "unknown column" in str(e).lower():
            print("💡 This looks like a database schema issue.")
            print("   The Investment table needs to be updated with new columns.")
            print("   You may need to:")
            print("   1. Drop and recreate the database")
            print("   2. Or run database migrations")
            print("   3. Or manually add the missing columns")
        
        return False
    finally:
        try:
            db.close()
        except:
            pass

def check_investment_table_schema():
    """Check the current investment table schema"""
    
    print("\n🏗️ Checking Investment Table Schema")
    
    try:
        from sqlalchemy import inspect
        from app.db.session import engine
        
        inspector = inspect(engine)
        
        if 'investments' in inspector.get_table_names():
            columns = inspector.get_columns('investments')
            column_names = [col['name'] for col in columns]
            
            print("📋 Current Investment table columns:")
            for col in columns:
                print(f"   - {col['name']}: {col['type']}")
            
            # Check for required new columns
            required_columns = ['title', 'investment_type', 'duration_months', 'description', 'location']
            missing_columns = [col for col in required_columns if col not in column_names]
            
            if missing_columns:
                print(f"\n❌ Missing columns: {missing_columns}")
                print("💡 The database schema needs to be updated")
                return False
            else:
                print("\n✅ All required columns are present")
                return True
        else:
            print("❌ Investment table does not exist")
            return False
            
    except Exception as e:
        print(f"❌ Schema check error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 DreamBig Investment Creation Test")
    print("=" * 50)
    
    # Check if server is running
    try:
        response = requests.get("http://127.0.0.1:8000/")
        if response.status_code != 200:
            print("❌ Server is not responding correctly")
            exit(1)
        print("✅ Server is running")
    except:
        print("❌ Server is not running!")
        print("   Please start the server with:")
        print("   python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        exit(1)
    
    # Check database schema
    schema_ok = check_investment_table_schema()
    
    # Test database investment creation
    if schema_ok:
        db_ok = test_database_investment_creation()
    else:
        print("⏭️ Skipping database test due to schema issues")
        db_ok = False
    
    # Test API endpoint
    api_ok = test_investment_creation()
    
    print("\n" + "=" * 50)
    print("🎯 Manual Testing Instructions:")
    print("1. 🚀 Start server: python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
    print("2. 🌐 Visit: http://127.0.0.1:8000/investments")
    print("3. 🔑 Login with your account")
    print("4. 💰 Click 'New Investment' button")
    print("5. 📝 Fill out the investment form")
    print("6. 💾 Click 'Create Investment'")
    print("7. ✅ Check for success message")
    
    if not schema_ok:
        print("\n⚠️ Database Schema Update Needed:")
        print("   The Investment table needs new columns.")
        print("   You can either:")
        print("   1. Recreate the database (will lose existing data)")
        print("   2. Manually add the missing columns")
        print("   3. Use database migrations (if available)")
    
    if schema_ok and db_ok and api_ok:
        print("\n🎉 All tests passed! Investment creation should work.")
    else:
        print("\n❌ Some tests failed - check the errors above")
