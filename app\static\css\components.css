/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--surface-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 400px;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-content h2 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
    transition: var(--transition);
}

.close:hover {
    color: var(--error-color);
}

/* Form Styles */
.form-group {
    margin-bottom: 1rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--surface-color);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-error {
    color: var(--error-color);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.modal-footer {
    text-align: center;
    margin-top: 1.5rem;
    color: var(--text-secondary);
}

.modal-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.modal-footer a:hover {
    text-decoration: underline;
}

/* Loading Spinner */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3000;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 100px;
    right: 20px;
    z-index: 2500;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.toast {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    box-shadow: var(--shadow-lg);
    min-width: 300px;
    animation: toastSlideIn 0.3s ease;
    position: relative;
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--error-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

.toast.info {
    border-left: 4px solid var(--primary-color);
}

@keyframes toastSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.toast-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.toast-title {
    font-weight: 600;
    color: var(--text-primary);
}

.toast-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0;
}

.toast-close:hover {
    color: var(--text-primary);
}

.toast-message {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Property Card List View */
.properties-grid.list-view {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.properties-grid.list-view .property-card {
    display: flex;
    align-items: center;
}

.properties-grid.list-view .property-image {
    width: 200px;
    height: 150px;
    flex-shrink: 0;
}

.properties-grid.list-view .property-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.properties-grid.list-view .property-info {
    flex: 1;
}

.properties-grid.list-view .property-actions {
    flex-shrink: 0;
    margin-left: 1rem;
}

/* Advanced Filters Modal */
.filters-modal .modal-content {
    max-width: 600px;
    width: 95%;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.price-range {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.price-range input {
    flex: 1;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 0.5rem;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-item input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* Property Detail Modal */
.property-detail-modal .modal-content {
    max-width: 800px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
}

.property-gallery {
    margin-bottom: 1.5rem;
}

.property-gallery img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.property-info-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.property-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: var(--background-color);
    border-radius: var(--border-radius);
}

.property-description {
    margin: 1rem 0;
    line-height: 1.6;
    color: var(--text-secondary);
}

.property-contact {
    background: var(--background-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-top: 1rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.contact-info span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Responsive adjustments for components */
@media (max-width: 768px) {
    .modal-content {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    .toast-container {
        right: 10px;
        left: 10px;
    }
    
    .toast {
        min-width: auto;
    }
    
    .properties-grid.list-view .property-card {
        flex-direction: column;
        align-items: stretch;
    }
    
    .properties-grid.list-view .property-image {
        width: 100%;
        height: 200px;
    }
    
    .properties-grid.list-view .property-content {
        flex-direction: column;
        align-items: stretch;
    }
    
    .property-info-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .filters-grid {
        grid-template-columns: 1fr;
    }
}
/* Remove all .property-* and .properties-grid related CSS from here (lines 220-320) */
