// Dashboard module for DreamBig Real Estate Platform

class DashboardManager {
    constructor() {
        this.favorites = [];
        this.recentlyViewed = [];
        this.analytics = null;
        this.recommendations = [];
        
        this.setupEventListeners();
    }
    
    // Setup event listeners
    setupEventListeners() {
        // Listen for auth state changes
        document.addEventListener('authStateChanged', (e) => {
            if (e.detail.isAuthenticated) {
                this.loadDashboardData();
            } else {
                this.clearDashboard();
            }
        });
    }
    
    // Load all dashboard data
    async loadDashboardData() {
        if (!authManager.isAuthenticated()) {
            return;
        }
        
        await Promise.all([
            this.loadFavorites(),
            this.loadRecentlyViewed(),
            this.loadAnalytics(),
            this.loadRecommendations()
        ]);
        
        this.renderDashboard();
    }
    
    // Load user favorites
    async loadFavorites() {
        const response = await ApiUtils.handleApiCall(
            () => apiClient.getFavorites(),
            { showLoading: false, suppressNotifications: true }
        );
        
        if (response) {
            this.favorites = response;
        }
    }
    
    // Load recently viewed properties
    async loadRecentlyViewed() {
        const response = await ApiUtils.handleApiCall(
            () => apiClient.getRecentlyViewed(),
            { showLoading: false, suppressNotifications: true }
        );
        
        if (response) {
            this.recentlyViewed = response;
        }
    }
    
    // Load user analytics
    async loadAnalytics() {
        const response = await ApiUtils.handleApiCall(
            () => apiClient.getUserAnalytics(),
            { showLoading: false }
        );
        
        if (response) {
            this.analytics = response;
        }
    }
    
    // Load recommendations
    async loadRecommendations() {
        const response = await ApiUtils.handleApiCall(
            () => apiClient.getRecommendations(),
            { showLoading: false }
        );
        
        if (response) {
            this.recommendations = response;
        }
    }
    
    // Clear dashboard data
    clearDashboard() {
        this.favorites = [];
        this.recentlyViewed = [];
        this.analytics = null;
        this.recommendations = [];
        
        const dashboardSection = document.getElementById('dashboard');
        if (dashboardSection) {
            dashboardSection.style.display = 'none';
        }
    }
    
    // Render dashboard
    renderDashboard() {
        this.renderFavorites();
        this.renderRecentlyViewed();
        this.renderAnalytics();
    }
    
    // Render favorites section
    renderFavorites() {
        const container = document.getElementById('favorites-list');
        if (!container) return;
        
        if (this.favorites.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-heart" style="font-size: 2rem; color: var(--text-secondary); margin-bottom: 1rem;"></i>
                    <p>No favorite properties yet</p>
                    <button class="btn btn-outline" onclick="showSection('properties')">Browse Properties</button>
                </div>
            `;
            return;
        }
        
        container.innerHTML = `
            <div class="dashboard-items">
                ${this.favorites.slice(0, 5).map(favorite => this.createDashboardPropertyCard(favorite.property)).join('')}
            </div>
            ${this.favorites.length > 5 ? `
                <div class="dashboard-footer">
                    <button class="btn btn-outline" onclick="showAllFavorites()">View All (${this.favorites.length})</button>
                </div>
            ` : ''}
        `;
    }
    
    // Render recently viewed section
    renderRecentlyViewed() {
        const container = document.getElementById('recently-viewed-list');
        if (!container) return;
        
        if (this.recentlyViewed.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-eye" style="font-size: 2rem; color: var(--text-secondary); margin-bottom: 1rem;"></i>
                    <p>No recently viewed properties</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = `
            <div class="dashboard-items">
                ${this.recentlyViewed.slice(0, 5).map(item => this.createDashboardPropertyCard(item.property, item.viewed_at)).join('')}
            </div>
            ${this.recentlyViewed.length > 5 ? `
                <div class="dashboard-footer">
                    <button class="btn btn-outline" onclick="showAllRecentlyViewed()">View All (${this.recentlyViewed.length})</button>
                </div>
            ` : ''}
        `;
    }
    
    // Render analytics section
    renderAnalytics() {
        const container = document.getElementById('user-analytics');
        if (!container || !this.analytics) return;
        
        container.innerHTML = `
            <div class="analytics-grid">
                <div class="analytics-item">
                    <div class="analytics-value">${this.analytics.total_favorites || 0}</div>
                    <div class="analytics-label">Favorites</div>
                </div>
                
                <div class="analytics-item">
                    <div class="analytics-value">${this.analytics.total_viewed || 0}</div>
                    <div class="analytics-label">Viewed</div>
                </div>
                
                <div class="analytics-item">
                    <div class="analytics-value">${this.analytics.activity_score || 0}</div>
                    <div class="analytics-label">Activity Score</div>
                </div>
                
                <div class="analytics-item">
                    <div class="analytics-value">${UTILS.capitalize(this.analytics.user_type || 'casual')}</div>
                    <div class="analytics-label">User Type</div>
                </div>
            </div>
            
            <div class="analytics-insights">
                <h4>Insights</h4>
                <ul>
                    <li>You've been ${this.analytics.user_type === 'active' ? 'very active' : 'browsing casually'} on the platform</li>
                    <li>Your recommendation accuracy is ${this.analytics.recommendations_accuracy || 85}%</li>
                    <li>You prefer ${this.getMostViewedPropertyType()} properties</li>
                </ul>
            </div>
        `;
    }
    
    // Create dashboard property card
    createDashboardPropertyCard(property, timestamp = null) {
        return `
            <div class="dashboard-property-item" onclick="showPropertyDetails(${property.id})">
                <div class="property-thumbnail">
                    <img src="${property.images?.[0]?.url || '/static/images/property-placeholder.jpg'}" 
                         alt="${property.title}"
                         onerror="this.src='/static/images/property-placeholder.jpg'">
                </div>
                
                <div class="property-info">
                    <h4>${UTILS.truncateText(property.title, 50)}</h4>
                    <p class="property-location">
                        <i class="fas fa-map-marker-alt"></i>
                        ${property.location}
                    </p>
                    <p class="property-price">${UTILS.formatCurrency(property.price)}</p>
                    ${timestamp ? `<p class="property-timestamp">${UTILS.formatRelativeTime(timestamp)}</p>` : ''}
                </div>
                
                <div class="property-actions">
                    <button class="btn-icon" onclick="event.stopPropagation(); toggleFavorite(${property.id})" title="Toggle favorite">
                        <i class="fas fa-heart"></i>
                    </button>
                </div>
            </div>
        `;
    }
    
    // Get most viewed property type
    getMostViewedPropertyType() {
        if (!this.recentlyViewed.length) return 'residential';
        
        const typeCounts = {};
        this.recentlyViewed.forEach(item => {
            const type = item.property.property_type;
            typeCounts[type] = (typeCounts[type] || 0) + 1;
        });
        
        return Object.keys(typeCounts).reduce((a, b) => 
            typeCounts[a] > typeCounts[b] ? a : b
        );
    }
    
    // Show user profile
    showProfile() {
        const user = authManager.getCurrentUser();
        if (!user) return;
        
        const modalHtml = `
            <div id="profile-modal" class="modal">
                <div class="modal-content">
                    <span class="close" onclick="closeModal('profile-modal')">&times;</span>
                    <h2>User Profile</h2>
                    
                    <form id="profile-form">
                        <div class="form-group">
                            <label>Name</label>
                            <input type="text" id="profile-name" value="${user.name || ''}" required>
                        </div>
                        
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" id="profile-email" value="${user.email || ''}" readonly>
                        </div>
                        
                        <div class="form-group">
                            <label>Phone</label>
                            <input type="tel" id="profile-phone" value="${user.phone || ''}" required>
                        </div>
                        
                        <div class="form-group">
                            <label>Role</label>
                            <select id="profile-role" disabled>
                                <option value="tenant" ${user.role === 'tenant' ? 'selected' : ''}>Tenant</option>
                                <option value="owner" ${user.role === 'owner' ? 'selected' : ''}>Property Owner</option>
                                <option value="investor" ${user.role === 'investor' ? 'selected' : ''}>Investor</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>KYC Status</label>
                            <div class="kyc-status ${user.kyc_verified ? 'verified' : 'pending'}">
                                <i class="fas ${user.kyc_verified ? 'fa-check-circle' : 'fa-clock'}"></i>
                                ${user.kyc_verified ? 'Verified' : 'Pending Verification'}
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-full">Update Profile</button>
                    </form>
                </div>
            </div>
        `;
        
        // Remove existing modal
        const existingModal = document.getElementById('profile-modal');
        if (existingModal) {
            existingModal.remove();
        }
        
        // Add new modal
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        document.getElementById('profile-modal').classList.add('show');
        
        // Setup form submission
        document.getElementById('profile-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.updateProfile();
        });
    }
    
    // Update user profile
    async updateProfile() {
        const name = document.getElementById('profile-name').value.trim();
        const phone = document.getElementById('profile-phone').value.trim();

        // Validation
        if (!name || !phone) {
            showToast('Please fill in all required fields', 'warning');
            return;
        }

        if (name.length < 2) {
            showToast('Name must be at least 2 characters long', 'warning');
            return;
        }

        // Clean phone number (remove any non-digits)
        const cleanPhone = phone.replace(/\D/g, '');

        if (cleanPhone.length < 10) {
            showToast('Please enter a valid phone number (at least 10 digits)', 'warning');
            return;
        }

        console.log('Updating profile with:', { name, phone: cleanPhone });

        const response = await ApiUtils.handleApiCall(
            () => apiClient.updateUser({ name, phone: cleanPhone }),
            {
                successMessage: 'Profile updated successfully',
                errorMessage: 'Failed to update profile',
                showLoading: true
            }
        );

        if (response) {
            // Update current user data
            authManager.currentUser = { ...authManager.currentUser, name, phone: cleanPhone };
            authManager.updateUI(true);
            closeModal('profile-modal');
        }
    }
}

// Initialize dashboard manager
const dashboardManager = new DashboardManager();

// Global functions
window.showDashboard = () => {
    if (!authManager.isAuthenticated()) {
        showToast('Please login to view dashboard', 'warning');
        showLoginModal();
        return;
    }

    // Navigate to dashboard page
    window.location.href = '/dashboard';
};

window.showProfile = () => {
    dashboardManager.showProfile();
};

window.showFavorites = () => {
    showToast('Favorites page coming soon!', 'info');
};

window.showAllFavorites = () => {
    showToast('All favorites view coming soon!', 'info');
};

window.showAllRecentlyViewed = () => {
    showToast('All recently viewed view coming soon!', 'info');
};

// Export dashboard manager
window.dashboardManager = dashboardManager;
