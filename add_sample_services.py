#!/usr/bin/env python3
"""
Script to add sample service providers to the database
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app.db import crud, models

def add_sample_services():
    """Add sample service providers to the database"""
    db = SessionLocal()
    
    try:
        # Sample service providers data
        sample_providers = [
            {
                "name": "Legal Associates",
                "service_type": "legal",
                "description": "Expert legal advice for property transactions, documentation, and compliance",
                "contact_number": "**********",
                "email": "<EMAIL>",
                "is_verified": True
            },
            {
                "name": "Finance Pro",
                "service_type": "financial",
                "description": "Get the best home loan deals with expert guidance and financial planning",
                "contact_number": "**********",
                "email": "<EMAIL>",
                "is_verified": True
            },
            {
                "name": "Fix It All",
                "service_type": "maintenance",
                "description": "Complete property maintenance and repair services for residential and commercial properties",
                "contact_number": "**********",
                "email": "<EMAIL>",
                "is_verified": True
            },
            {
                "name": "Design Studio",
                "service_type": "interior",
                "description": "Transform your space with professional interior design and decoration services",
                "contact_number": "9876543213",
                "email": "<EMAIL>",
                "is_verified": True
            },
            {
                "name": "Move Easy",
                "service_type": "moving",
                "description": "Safe and reliable moving services for homes and offices with professional packing",
                "contact_number": "9876543214",
                "email": "<EMAIL>",
                "is_verified": True
            },
            {
                "name": "Property Lawyers",
                "service_type": "legal",
                "description": "Specialized legal services for property disputes, contracts, and registration",
                "contact_number": "9876543215",
                "email": "<EMAIL>",
                "is_verified": True
            },
            {
                "name": "Home Loan Experts",
                "service_type": "financial",
                "description": "Mortgage and home loan consultation with competitive rates and quick approval",
                "contact_number": "**********",
                "email": "<EMAIL>",
                "is_verified": True
            },
            {
                "name": "Quick Repairs",
                "service_type": "maintenance",
                "description": "Emergency repair services for plumbing, electrical, and general maintenance",
                "contact_number": "**********",
                "email": "<EMAIL>",
                "is_verified": True
            }
        ]
        
        # Check if service providers already exist
        existing_providers = db.query(models.ServiceProvider).count()
        if existing_providers > 0:
            print(f"Found {existing_providers} existing service providers. Skipping sample data creation.")
            return
        
        # Add sample service providers
        created_count = 0
        for provider_data in sample_providers:
            try:
                provider = crud.create_service_provider(db, provider_data)
                print(f"✅ Created service provider: {provider.name} ({provider.service_type})")
                created_count += 1
            except Exception as e:
                print(f"❌ Error creating provider {provider_data['name']}: {str(e)}")
        
        print(f"\n🎉 Successfully created {created_count} service providers!")
        print("\nService providers by category:")
        
        # Show summary by category
        categories = db.query(models.ServiceProvider.service_type).distinct().all()
        for (category,) in categories:
            count = db.query(models.ServiceProvider).filter(
                models.ServiceProvider.service_type == category
            ).count()
            print(f"  - {category.title()}: {count} providers")
            
    except Exception as e:
        print(f"❌ Error adding sample services: {str(e)}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 Adding sample service providers to database...")
    add_sample_services()
    print("✨ Done!")
