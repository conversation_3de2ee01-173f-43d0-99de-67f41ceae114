#!/usr/bin/env python3
"""
Update Investment table schema to support new investment features
"""

from sqlalchemy import text
from app.db.session import engine

def update_investment_schema():
    """Add new columns to the Investment table"""
    
    print("🔧 Updating Investment Table Schema")
    print("=" * 40)
    
    # SQL commands to add new columns
    alter_commands = [
        "ALTER TABLE investments ADD COLUMN title VARCHAR(200)",
        "ALTER TABLE investments ADD COLUMN investment_type VARCHAR(50)",
        "ALTER TABLE investments ADD COLUMN duration_months INTEGER",
        "ALTER TABLE investments ADD COLUMN description TEXT",
        "ALTER TABLE investments ADD COLUMN location VARCHAR(255)",
        "ALTER TABLE investments ALTER COLUMN property_id DROP NOT NULL"  # Make property_id optional
    ]
    
    try:
        with engine.connect() as connection:
            for command in alter_commands:
                try:
                    print(f"📝 Executing: {command}")
                    connection.execute(text(command))
                    connection.commit()
                    print("✅ Success")
                except Exception as e:
                    if "already exists" in str(e).lower() or "duplicate column" in str(e).lower():
                        print("⏭️ Column already exists, skipping")
                    else:
                        print(f"❌ Error: {str(e)}")
                        # Continue with other commands
            
            print("\n🎉 Schema update completed!")
            return True
            
    except Exception as e:
        print(f"❌ Schema update failed: {str(e)}")
        return False

def verify_schema():
    """Verify the schema was updated correctly"""
    
    print("\n🔍 Verifying Updated Schema")
    print("=" * 40)
    
    try:
        from sqlalchemy import inspect
        
        inspector = inspect(engine)
        columns = inspector.get_columns('investments')
        column_names = [col['name'] for col in columns]
        
        print("📋 Current Investment table columns:")
        for col in columns:
            print(f"   - {col['name']}: {col['type']}")
        
        # Check for required columns
        required_columns = ['title', 'investment_type', 'duration_months', 'description', 'location']
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            print(f"\n❌ Still missing columns: {missing_columns}")
            return False
        else:
            print("\n✅ All required columns are present!")
            return True
            
    except Exception as e:
        print(f"❌ Schema verification failed: {str(e)}")
        return False

def test_investment_creation():
    """Test creating an investment with the new schema"""
    
    print("\n🧪 Testing Investment Creation")
    print("=" * 40)
    
    try:
        from app.db.session import get_db
        from app.db import crud
        from app.db.models import InvestmentRiskLevel
        from datetime import datetime, timedelta
        
        db = next(get_db())
        
        # Find a test user
        users = crud.get_users(db, skip=0, limit=1)
        if not users:
            print("❌ No users found in database")
            return False
        
        test_user = users[0]
        print(f"📝 Testing with user: {test_user.email}")
        
        # Test investment data with new fields
        investment_data = {
            "title": "Updated Schema Test Investment",
            "amount": 75000.0,
            "expected_roi": 15.0,
            "risk_level": InvestmentRiskLevel.MEDIUM,
            "investment_type": "development",
            "duration_months": 18,
            "description": "Test investment with new schema fields",
            "location": "Bangalore, Karnataka",
            "start_date": datetime.now(),
            "end_date": datetime.now() + timedelta(days=18 * 30),
            "status": "active"
        }
        
        # Create investment
        db_investment = crud.create_investment(
            db=db, 
            investment_data=investment_data, 
            investor_id=test_user.id
        )
        
        if db_investment:
            print("✅ Investment creation successful!")
            print(f"   ID: {db_investment.id}")
            print(f"   Title: {db_investment.title}")
            print(f"   Type: {db_investment.investment_type}")
            print(f"   Duration: {db_investment.duration_months} months")
            print(f"   Location: {db_investment.location}")
            
            # Clean up
            db.delete(db_investment)
            db.commit()
            print("🧹 Test investment cleaned up")
            
            return True
        else:
            print("❌ Investment creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False
    finally:
        try:
            db.close()
        except:
            pass

if __name__ == "__main__":
    print("🚀 DreamBig Investment Schema Update")
    print("=" * 50)
    
    # Update schema
    update_ok = update_investment_schema()
    
    if update_ok:
        # Verify schema
        verify_ok = verify_schema()
        
        if verify_ok:
            # Test investment creation
            test_ok = test_investment_creation()
            
            if test_ok:
                print("\n🎉 Schema update completed successfully!")
                print("💡 You can now create investments with the new features:")
                print("   - Investment title")
                print("   - Investment type (property, rental, development, etc.)")
                print("   - Duration in months")
                print("   - Description")
                print("   - Location")
                print("   - Optional property association")
                
                print("\n🌐 Test the investment creation:")
                print("   1. Visit: http://127.0.0.1:8000/investments")
                print("   2. Login to your account")
                print("   3. Click 'New Investment'")
                print("   4. Fill out the form and submit")
            else:
                print("\n❌ Schema updated but investment creation test failed")
        else:
            print("\n❌ Schema verification failed")
    else:
        print("\n❌ Schema update failed")
        
    print("\n💡 If you encounter any issues:")
    print("   - Make sure the server is not running during schema update")
    print("   - Check database permissions")
    print("   - Backup your database before making changes")
