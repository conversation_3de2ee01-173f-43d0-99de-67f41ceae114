{% extends "base.html" %}

{% block title %}Investments - DreamBig Real Estate Platform{% endblock %}

{% block content %}
    <!-- Investments Section -->
    <section class="investments-section" style="margin-top: 80px; padding-top: 2rem;">
        <div class="container">
            <div class="section-header">
                <h2>Investment Opportunities</h2>
                <div class="investment-actions">
                    <button class="btn btn-primary" onclick="showCreateInvestmentModal()">
                        <i class="fas fa-plus"></i> New Investment
                    </button>
                </div>
            </div>
            
            <!-- Investment Stats -->
            <div class="investment-stats" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                <div class="stat-card" style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--border-radius); text-align: center;">
                    <div class="stat-value" style="font-size: 2rem; font-weight: 700; color: var(--primary-color);">₹0</div>
                    <div class="stat-label" style="color: var(--text-secondary);">Total Invested</div>
                </div>
                
                <div class="stat-card" style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--border-radius); text-align: center;">
                    <div class="stat-value" style="font-size: 2rem; font-weight: 700; color: var(--success-color);">₹0</div>
                    <div class="stat-label" style="color: var(--text-secondary);">Current Value</div>
                </div>
                
                <div class="stat-card" style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--border-radius); text-align: center;">
                    <div class="stat-value" style="font-size: 2rem; font-weight: 700; color: var(--accent-color);">0%</div>
                    <div class="stat-label" style="color: var(--text-secondary);">Average ROI</div>
                </div>
                
                <div class="stat-card" style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--border-radius); text-align: center;">
                    <div class="stat-value" style="font-size: 2rem; font-weight: 700; color: var(--primary-color);">0</div>
                    <div class="stat-label" style="color: var(--text-secondary);">Active Investments</div>
                </div>
            </div>
            
            <!-- Investment Filters -->
            <div class="investment-filters" style="margin-bottom: 2rem;">
                <div class="filter-group" style="display: flex; gap: 1rem; flex-wrap: wrap;">
                    <select id="risk-filter" style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: var(--border-radius);">
                        <option value="">All Risk Levels</option>
                        <option value="low">Low Risk</option>
                        <option value="medium">Medium Risk</option>
                        <option value="high">High Risk</option>
                    </select>
                    
                    <select id="status-filter" style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: var(--border-radius);">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="completed">Completed</option>
                        <option value="pending">Pending</option>
                    </select>
                    
                    <input type="number" id="min-amount" placeholder="Min Amount" style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: var(--border-radius);">
                    <input type="number" id="max-amount" placeholder="Max Amount" style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: var(--border-radius);">
                </div>
            </div>
            
            <!-- Investments Grid -->
            <div class="investments-grid" id="investments-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 2rem;">
                <!-- Investments will be loaded here -->
            </div>
            
            <!-- Empty State -->
            <div id="investments-empty" class="empty-state" style="text-align: center; padding: 3rem 1rem; color: var(--text-secondary);">
                <i class="fas fa-chart-line" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                <h3>No Investments Yet</h3>
                <p>Start your investment journey by exploring available opportunities</p>
                <div style="margin-top: 2rem;">
                    <button class="btn btn-primary" onclick="showCreateInvestmentModal()">Create Investment</button>
                    <button class="btn btn-outline" onclick="window.location.href='/properties'">Browse Properties</button>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Authentication Required Message -->
    <div id="auth-required" class="auth-required" style="display: none; text-align: center; padding: 4rem 2rem; margin-top: 80px;">
        <div class="container">
            <i class="fas fa-lock" style="font-size: 3rem; color: var(--text-secondary); margin-bottom: 1rem;"></i>
            <h2>Authentication Required</h2>
            <p>Please login to view your investments</p>
            <div style="margin-top: 2rem;">
                <button class="btn btn-primary" onclick="showLoginModal()">Login</button>
                <button class="btn btn-outline" onclick="showRegisterModal()">Sign Up</button>
            </div>
        </div>
    </div>

    <!-- Create Investment Modal -->
    <div id="create-investment-modal" class="modal">
        <div class="modal-content" style="max-width: 600px;">
            <span class="close" onclick="closeModal('create-investment-modal')">&times;</span>
            <h2>Create New Investment</h2>

            <form id="create-investment-form">
                <div class="form-group">
                    <label>Investment Title</label>
                    <input type="text" id="investment-title" placeholder="e.g., Downtown Apartment Investment" required>
                </div>

                <div class="form-group">
                    <label>Investment Amount (₹)</label>
                    <input type="number" id="investment-amount" placeholder="100000" min="1000" required>
                </div>

                <div class="form-group">
                    <label>Expected ROI (%)</label>
                    <input type="number" id="investment-roi" placeholder="12" min="1" max="100" step="0.1" required>
                </div>

                <div class="form-group">
                    <label>Investment Duration (months)</label>
                    <input type="number" id="investment-duration" placeholder="12" min="1" max="360" required>
                </div>

                <div class="form-group">
                    <label>Risk Level</label>
                    <select id="investment-risk" required>
                        <option value="">Select Risk Level</option>
                        <option value="low">Low Risk</option>
                        <option value="medium">Medium Risk</option>
                        <option value="high">High Risk</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>Investment Type</label>
                    <select id="investment-type" required>
                        <option value="">Select Investment Type</option>
                        <option value="property">Property Investment</option>
                        <option value="rental">Rental Income</option>
                        <option value="development">Property Development</option>
                        <option value="reit">Real Estate Investment Trust</option>
                        <option value="commercial">Commercial Real Estate</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>Description</label>
                    <textarea id="investment-description" placeholder="Describe your investment opportunity..." rows="4"></textarea>
                </div>

                <div class="form-group">
                    <label>Location</label>
                    <input type="text" id="investment-location" placeholder="e.g., Mumbai, Maharashtra">
                </div>

                <button type="submit" class="btn btn-primary btn-full">Create Investment</button>
            </form>
        </div>
    </div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Initialize investments page
    document.addEventListener('DOMContentLoaded', function() {
        // Check if user is authenticated
        setTimeout(function() {
            if (authManager && authManager.isAuthenticated()) {
                document.querySelector('.investments-section').style.display = 'block';
                document.getElementById('auth-required').style.display = 'none';
                loadInvestments();
            } else {
                document.querySelector('.investments-section').style.display = 'none';
                document.getElementById('auth-required').style.display = 'block';
            }
        }, 500);
    });
    
    // Listen for auth state changes
    document.addEventListener('authStateChanged', function(e) {
        if (e.detail.isAuthenticated) {
            document.querySelector('.investments-section').style.display = 'block';
            document.getElementById('auth-required').style.display = 'none';
            loadInvestments();
        } else {
            document.querySelector('.investments-section').style.display = 'none';
            document.getElementById('auth-required').style.display = 'block';
        }
    });
    
    // Load investments
    async function loadInvestments() {
        if (!apiClient) return;

        try {
            const response = await ApiUtils.handleApiCall(
                () => apiClient.getInvestments(),
                { showLoading: true }
            );

            if (response) {
                console.log('Loaded investments:', response);
                renderInvestments(response);
            } else {
                // If no response, show empty state
                renderInvestments([]);
            }
        } catch (error) {
            console.error('Error loading investments:', error);
            renderInvestments([]);
        }
    }
    
    // Render investments
    function renderInvestments(investments) {
        const grid = document.getElementById('investments-grid');
        const emptyState = document.getElementById('investments-empty');
        
        if (investments.length === 0) {
            grid.style.display = 'none';
            emptyState.style.display = 'block';
            return;
        }
        
        grid.style.display = 'grid';
        emptyState.style.display = 'none';
        
        grid.innerHTML = investments.map(investment => createInvestmentCard(investment)).join('');
    }
    
    // Create investment card
    function createInvestmentCard(investment) {
        const riskColor = {
            low: 'var(--success-color)',
            medium: 'var(--warning-color)',
            high: 'var(--error-color)'
        };

        return `
            <div class="investment-card" style="background: var(--surface-color); border-radius: var(--border-radius); padding: 1.5rem; box-shadow: var(--shadow-sm);">
                <div class="investment-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                    <h3 style="color: var(--primary-color);">${investment.title || `Investment #${investment.id}`}</h3>
                    <span class="risk-badge" style="background: ${riskColor[investment.risk_level]}; color: white; padding: 0.25rem 0.75rem; border-radius: 20px; font-size: 0.75rem;">
                        ${investment.risk_level.toUpperCase()} RISK
                    </span>
                </div>

                ${investment.investment_type ? `
                    <div class="investment-type" style="margin-bottom: 1rem;">
                        <span class="type-badge" style="background: var(--background-color); padding: 0.25rem 0.75rem; border-radius: 15px; font-size: 0.75rem; color: var(--text-secondary);">
                            ${UTILS.capitalize(investment.investment_type)}
                        </span>
                    </div>
                ` : ''}

                <div class="investment-details" style="margin-bottom: 1rem;">
                    <div class="detail-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>Amount:</span>
                        <strong>${UTILS.formatCurrency(investment.amount)}</strong>
                    </div>
                    <div class="detail-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>Expected ROI:</span>
                        <strong>${investment.expected_roi}%</strong>
                    </div>
                    ${investment.duration_months ? `
                        <div class="detail-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                            <span>Duration:</span>
                            <strong>${investment.duration_months} months</strong>
                        </div>
                    ` : ''}
                    ${investment.location ? `
                        <div class="detail-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                            <span>Location:</span>
                            <span>${investment.location}</span>
                        </div>
                    ` : ''}
                    <div class="detail-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>Status:</span>
                        <span class="status-badge" style="color: var(--primary-color); font-weight: 500;">${UTILS.capitalize(investment.status)}</span>
                    </div>
                    <div class="detail-row" style="display: flex; justify-content: space-between;">
                        <span>Start Date:</span>
                        <span>${UTILS.formatDate(investment.start_date)}</span>
                    </div>
                </div>

                ${investment.description ? `
                    <div class="investment-description" style="margin-bottom: 1rem; padding: 0.75rem; background: var(--background-color); border-radius: var(--border-radius); font-size: 0.875rem; color: var(--text-secondary);">
                        ${UTILS.truncateText(investment.description, 100)}
                    </div>
                ` : ''}

                <div class="investment-actions" style="display: flex; gap: 0.5rem;">
                    <button class="btn btn-outline" onclick="viewInvestmentDetails(${investment.id})" style="flex: 1;">
                        View Details
                    </button>
                    <button class="btn-icon" onclick="shareInvestment(${investment.id})" title="Share">
                        <i class="fas fa-share-alt"></i>
                    </button>
                </div>
            </div>
        `;
    }
    
    // Investment actions
    function showCreateInvestmentModal() {
        if (!authManager.isAuthenticated()) {
            showToast('Please login to create investments', 'warning');
            showLoginModal();
            return;
        }

        document.getElementById('create-investment-modal').classList.add('show');
    }

    // Handle investment creation form
    document.getElementById('create-investment-form').addEventListener('submit', async function(e) {
        e.preventDefault();

        const formData = {
            title: document.getElementById('investment-title').value.trim(),
            amount: parseFloat(document.getElementById('investment-amount').value),
            expected_roi: parseFloat(document.getElementById('investment-roi').value),
            duration_months: parseInt(document.getElementById('investment-duration').value),
            risk_level: document.getElementById('investment-risk').value,
            investment_type: document.getElementById('investment-type').value,
            description: document.getElementById('investment-description').value.trim(),
            location: document.getElementById('investment-location').value.trim()
        };

        // Validation
        if (!formData.title || !formData.amount || !formData.expected_roi ||
            !formData.duration_months || !formData.risk_level || !formData.investment_type) {
            showToast('Please fill in all required fields', 'warning');
            return;
        }

        if (formData.amount < 1000) {
            showToast('Minimum investment amount is ₹1,000', 'warning');
            return;
        }

        if (formData.expected_roi < 1 || formData.expected_roi > 100) {
            showToast('Expected ROI must be between 1% and 100%', 'warning');
            return;
        }

        try {
            showLoading(true);

            const response = await ApiUtils.handleApiCall(
                () => apiClient.createInvestment(formData),
                {
                    successMessage: 'Investment created successfully!',
                    errorMessage: 'Failed to create investment'
                }
            );

            if (response) {
                closeModal('create-investment-modal');
                document.getElementById('create-investment-form').reset();
                loadInvestments(); // Reload the investments list
            }

        } catch (error) {
            console.error('Investment creation error:', error);
            showToast('Failed to create investment: ' + error.message, 'error');
        } finally {
            showLoading(false);
        }
    });
    
    function viewInvestmentDetails(investmentId) {
        showToast('Investment details feature coming soon!', 'info');
    }
    
    function shareInvestment(investmentId) {
        showToast('Investment sharing feature coming soon!', 'info');
    }
</script>
{% endblock %}
