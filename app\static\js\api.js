// API utility functions for DreamBig Real Estate Platform

class ApiClient {
    constructor() {
        this.baseUrl = API_CONFIG.BASE_URL + API_CONFIG.API_VERSION;
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
    }
    
    // Get authorization headers
    getAuthHeaders() {
        try {
            if (typeof authManager !== 'undefined' && authManager.isAuthenticated()) {
                const token = authManager.getIdToken();
                if (token) {
                    return {
                        ...this.defaultHeaders,
                        'Authorization': `Bearer ${token}`
                    };
                }
            }
        } catch (error) {
            console.warn('Error getting auth token:', error);
        }
        return this.defaultHeaders;
    }
    
    // Generic API request method
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            headers: this.getAuthHeaders(),
            ...options
        };
        console.log(`Making API request to: ${url} with config:`, config);
        
        try {
            const response = await fetch(url, config);
            console.log(`Received response for ${url}:`, response);
            const data = await response.json();
            console.log(`Received data for ${url}:`, data);
            
            if (response.ok) {
                return { success: true, data, status: response.status };
            } else {
                // Handle token expiration
                if (response.status === 401) {
                    try {
                        await authManager.refreshToken();
                        // Retry request with new token
                        config.headers = this.getAuthHeaders();
                        const retryResponse = await fetch(url, config);
                        const retryData = await retryResponse.json();
                        
                        if (retryResponse.ok) {
                            return { success: true, data: retryData, status: retryResponse.status };
                        }
                    } catch (refreshError) {
                        console.error('Token refresh failed:', refreshError);
                        authManager.logout();
                    }
                }
                
                return {
                    success: false,
                    error: data.detail || data.message || 'Request failed',
                    status: response.status
                };
            }
        } catch (error) {
            console.error('API request error:', error);
            return {
                success: false,
                error: 'Network error. Please check your connection.',
                status: 0
            };
        }
    }
    
    // GET request
    async get(endpoint, params = {}) {
        console.log('API GET request:', endpoint, params);

        try {
            const queryString = UTILS.getQueryString(params);
            const url = queryString ? `${endpoint}?${queryString}` : endpoint;
            console.log('Final URL:', url);

            const result = await this.request(url, {
                method: 'GET'
            });
            console.log('API GET result:', result);
            return result;
        } catch (error) {
            console.error('API GET error:', error);
            throw error;
        }
    }
    
    // POST request
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
    
    // PUT request
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }
    
    // DELETE request
    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }
    
    // Upload file
    async uploadFile(endpoint, file, additionalData = {}) {
        const formData = new FormData();
        formData.append('file', file);
        
        Object.keys(additionalData).forEach(key => {
            formData.append(key, additionalData[key]);
        });
        
        const token = authManager.getIdToken();
        const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
        
        return this.request(endpoint, {
            method: 'POST',
            headers,
            body: formData
        });
    }
    
    // Properties API
    async getProperties(params = {}) {
        return this.get(API_CONFIG.ENDPOINTS.PROPERTIES, params);
    }
    
    async getProperty(id) {
        return this.get(API_CONFIG.ENDPOINTS.PROPERTY_DETAIL.replace('{id}', id));
    }
    
    async createProperty(propertyData) {
        return this.post(API_CONFIG.ENDPOINTS.PROPERTIES, propertyData);
    }
    
    async updateProperty(id, propertyData) {
        return this.put(API_CONFIG.ENDPOINTS.PROPERTY_DETAIL.replace('{id}', id), propertyData);
    }
    
    async deleteProperty(id) {
        return this.delete(API_CONFIG.ENDPOINTS.PROPERTY_DETAIL.replace('{id}', id));
    }
    
    async uploadPropertyImage(propertyId, file) {
        const endpoint = API_CONFIG.ENDPOINTS.PROPERTY_UPLOAD_IMAGE.replace('{id}', propertyId);
        return this.uploadFile(endpoint, file);
    }
    
    // Search API
    async searchProperties(query, filters = {}) {
        const params = { query, ...filters };
        return this.get(API_CONFIG.ENDPOINTS.SEARCH, params);
    }
    
    // User API
    async getCurrentUser() {
        return this.get(API_CONFIG.ENDPOINTS.USER_ME);
    }
    
    async updateUser(userData) {
        return this.put(API_CONFIG.ENDPOINTS.USER_ME, userData);
    }
    
    async getUserPreferences() {
        return this.get(API_CONFIG.ENDPOINTS.USER_PREFERENCES);
    }
    
    async updateUserPreferences(preferences) {
        return this.put(API_CONFIG.ENDPOINTS.USER_PREFERENCES, preferences);
    }
    
    async getFavorites() {
        return this.get(API_CONFIG.ENDPOINTS.USER_FAVORITES);
    }
    
    async addToFavorites(propertyId) {
        return this.post(`${API_CONFIG.ENDPOINTS.USER_FAVORITES}/${propertyId}`);
    }
    
    async removeFromFavorites(propertyId) {
        return this.delete(`${API_CONFIG.ENDPOINTS.USER_FAVORITES}/${propertyId}`);
    }

    async getRecentlyViewed() {
        return this.get(API_CONFIG.ENDPOINTS.USER_RECENTLY_VIEWED);
    }

    async getRecommendations() {
        return this.get(API_CONFIG.ENDPOINTS.USER_RECOMMENDATIONS);
    }

    async getNotifications() {
        return this.get(API_CONFIG.ENDPOINTS.USER_NOTIFICATIONS);
    }

    async markNotificationAsRead(notificationId) {
        return this.post(`${API_CONFIG.ENDPOINTS.USER_NOTIFICATIONS}/${notificationId}/read`);
    }

    // Admin API
    async getAdminDashboardStats() {
        return this.get(API_CONFIG.ENDPOINTS.ADMIN_DASHBOARD_STATS);
    }

    async getAdminUsers() {
        return this.get(API_CONFIG.ENDPOINTS.ADMIN_USERS);
    }

    async getAdminProperties() {
        return this.get(API_CONFIG.ENDPOINTS.ADMIN_PROPERTIES);
    }

    async getAdminInvestments() {
        return this.get(API_CONFIG.ENDPOINTS.ADMIN_INVESTMENTS);
    }

    async getAdminServices() {
        return this.get(API_CONFIG.ENDPOINTS.ADMIN_SERVICES);
    }

    async getAdminServiceProviders() {
        return this.get(API_CONFIG.ENDPOINTS.ADMIN_SERVICE_PROVIDERS);
    }

    async getAdminServiceBookings() {
        return this.get(API_CONFIG.ENDPOINTS.ADMIN_SERVICE_BOOKINGS);
    }

    async getAdminAnalytics() {
        return this.get(API_CONFIG.ENDPOINTS.ADMIN_ANALYTICS);
    }
}

class ApiUtils {
    // Utility function to handle API calls, showing loading indicators and messages
    static async handleApiCall(apiCall, options = {}) {
        const { showLoading = true, showSuccess = false, showError = true, successMessage = 'Operation successful!', errorMessage = 'An error occurred.', suppressNotifications = false } = options;

        if (showLoading) {
            UTILS.showLoadingOverlay();
        }

        try {
            const response = await apiCall();

            if (response.success) {
                if (showSuccess && !suppressNotifications) {
                    UTILS.showToast('Success', successMessage, 'success');
                }
                return response.data;
            } else {
                if (showError && !suppressNotifications) {
                    UTILS.showToast('Error', response.error || errorMessage, 'error');
                }
                return null;
            }
        } catch (error) {
            console.error('API Call Error:', error);
            if (showError && !suppressNotifications) {
                UTILS.showToast('Error', errorMessage, 'error');
            }
            return null;
        } finally {
            if (showLoading) {
                UTILS.hideLoadingOverlay();
            }
        }
    }
}

// Paginated data fetcher
async function fetchPaginatedData(fetchFunction, params = {}) {
    const defaultParams = {
        skip: 0,
        limit: APP_CONFIG.PAGINATION.DEFAULT_LIMIT,
        ...params
    };

    return fetchFunction(defaultParams);
}

// Batch operations
async function batchUpdate(endpoint, data, successMessage = 'Batch update successful!') {
    return ApiUtils.handleApiCall(
        () => apiClient.post(`${endpoint}/batch`, data),
        { successMessage }
    );
}

async function batchDelete(endpoint, ids, successMessage = 'Batch delete successful!') {
    return ApiUtils.handleApiCall(
        () => apiClient.delete(`${endpoint}/batch`, { ids }),
        { successMessage }
    );
}

// Export API client and utilities
window.ApiUtils = ApiUtils;
